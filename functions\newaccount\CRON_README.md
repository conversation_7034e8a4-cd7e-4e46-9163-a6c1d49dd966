# 新账号调度任务说明

本目录包含两个自动调度任务，用于处理新账号的重置和初始化流程。

## 调度任务概览

### 1. 重置账号任务 (reset-accounts-cron.ts)
- **执行时间**: 每天早上 8:00 (Asia/Shanghai 时区)
- **Cron 表达式**: `0 8 * * *`
- **功能**: 获取符合条件的账号并发送到重置API

#### 筛选条件
- `resetStatus = 0` (未重置)
- `initStatus = 0` (未初始化)
- 限制数量: 10个账号

#### 处理流程
1. 查询数据库获取符合条件的账号
2. 准备账号数据 (id, email, password, proofEmail)
3. 发送 POST 请求到 `https://www.xxx.com/reset-account`
4. 记录执行结果和日志

### 2. 初始化账号任务 (init-accounts-cron.ts)
- **执行时间**: 每天晚上 20:00 (Asia/Shanghai 时区)
- **Cron 表达式**: `0 20 * * *`
- **功能**: 获取已重置但未初始化的账号并发送到初始化API

#### 筛选条件
- `resetStatus = 1` (重置成功)
- `initStatus = 0` (未初始化)
- `resetDatetime < 今天零点` (重置时间早于今天)
- 限制数量: 10个账号

#### 处理流程
1. 计算今天零点时间戳
2. 查询数据库获取符合条件的账号
3. 准备账号数据 (id, email, password, proofEmail, resetDatetime)
4. 发送 POST 请求到 `https://www.xxx.com/init-account`
5. 记录执行结果和日志

## 配置文件

### wrangler.jsonc
```json
{
  "triggers": {
    "crons": [
      "0 8 * * *",  // 每天早上8点执行 reset-accounts
      "0 20 * * *"  // 每天晚上20点执行 init-accounts
    ]
  }
}
```

## 调度处理器

### functions/scheduled.ts
主要的调度事件处理器，负责：
- 接收 Cloudflare Workers 的调度事件
- 根据当前时间调用相应的 cron 任务
- 提供手动触发接口用于测试

## 手动测试

### 1. 通过调度处理器测试
```bash
# 手动触发重置账号任务
curl -X POST http://localhost:8788/scheduled \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"task": "reset-accounts", "force": true}'

# 手动触发初始化账号任务
curl -X POST http://localhost:8788/scheduled \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"task": "init-accounts", "force": true}'
```

### 2. 直接测试 cron 任务
```bash
# 直接测试重置账号任务
curl -X POST http://localhost:8788/newaccount/reset-accounts-cron \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 直接测试初始化账号任务
curl -X POST http://localhost:8788/newaccount/init-accounts-cron \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 运行测试套件
```bash
npm run test:unit -- cron-tasks.spec.ts
```

## 日志和监控

### 日志格式
所有 cron 任务都会输出详细的日志信息：
- 执行开始时间
- 查询条件和结果
- API 调用状态和响应
- 错误信息（如果有）

### 监控要点
1. **账号数量**: 检查每次执行处理的账号数量
2. **API 响应**: 监控外部 API 的响应状态
3. **执行时间**: 确保任务在预期时间执行
4. **错误率**: 监控失败的任务执行

## 故障排除

### 常见问题
1. **没有符合条件的账号**: 正常情况，会返回成功状态但账号数量为0
2. **外部 API 失败**: 检查网络连接和 API 端点状态
3. **数据库查询失败**: 检查数据库连接和表结构

### 调试步骤
1. 检查 Cloudflare Workers 日志
2. 使用手动触发接口测试
3. 验证数据库中的账号状态
4. 检查外部 API 的可用性

## 部署

### 开发环境
```bash
wrangler dev --env dev
```

### 生产环境
```bash
wrangler deploy --env production
```

部署后，cron 任务将自动按照配置的时间表执行。

## 注意事项

1. **时区**: 所有时间都使用 Asia/Shanghai 时区
2. **API 端点**: 当前使用示例 URL，部署前需要更新为实际的 API 端点
3. **错误处理**: 任务失败不会影响其他调度任务的执行
4. **数据一致性**: 确保数据库状态字段的正确性
