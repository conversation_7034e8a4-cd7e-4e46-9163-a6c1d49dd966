# Cron 任务部署说明

## 概述

本项目已成功添加了两个调度事件（Cron 任务），用于自动处理新账号的重置和初始化流程。

## 已添加的文件

### 1. Cron 任务文件
- `functions/newaccount/reset-accounts-cron.ts` - 早上8点执行的重置账号任务
- `functions/newaccount/init-accounts-cron.ts` - 晚上20点执行的初始化账号任务
- `functions/scheduled.ts` - 主调度处理器（用于手动触发和测试）

### 2. 配置文件
- `wrangler.jsonc` - 已添加 cron 触发器配置

### 3. 测试文件
- `functions/__tests__/cron-tasks.spec.ts` - 完整的测试套件
- `functions/newaccount/CRON_README.md` - 详细的使用说明

## Cron 任务详情

### 重置账号任务 (每天 8:00)
- **触发时间**: 每天早上8点 (Asia/Shanghai 时区)
- **Cron 表达式**: `0 8 * * *`
- **功能**: 获取 `resetStatus=0 && initStatus=0` 的账号（最多10个）
- **目标API**: `https://www.xxx.com/reset-account`

### 初始化账号任务 (每天 20:00)
- **触发时间**: 每天晚上20点 (Asia/Shanghai 时区)
- **Cron 表达式**: `0 20 * * *`
- **功能**: 获取 `resetStatus=1 && initStatus=0 && resetDatetime < 今天零点` 的账号（最多10个）
- **目标API**: `https://www.xxx.com/init-account`

## 部署前的准备工作

### 1. 更新 API 端点
在部署到生产环境之前，需要将示例 URL 替换为实际的 API 端点：

**文件**: `functions/newaccount/reset-accounts-cron.ts`
```typescript
// 第 58 行，替换为实际的重置账号 API
const resetResponse = await fetch('https://your-actual-domain.com/reset-account', {
```

**文件**: `functions/newaccount/init-accounts-cron.ts`
```typescript
// 第 76 行，替换为实际的初始化账号 API
const initResponse = await fetch('https://your-actual-domain.com/init-account', {
```

### 2. 验证数据库结构
确保 `newaccounts` 表包含以下字段：
- `resetStatus` (integer): 0=未重置, 1=重置成功, 2=重置失败
- `initStatus` (integer): 0=未初始化, 1=初始化成功, 2=初始化失败
- `resetDatetime` (text): 重置时间戳

## 部署步骤

### 1. 构建项目
```bash
npm run build
```

### 2. 部署到开发环境
```bash
wrangler deploy --env dev
```

### 3. 部署到生产环境
```bash
wrangler deploy --env production
```

## 测试验证

### 1. 本地测试
```bash
# 启动开发服务器
npm run dev:workers

# 运行测试套件
npm run test:unit -- cron-tasks.spec.ts --run
```

### 2. 手动触发测试
```bash
# 测试重置账号任务
curl -X POST https://your-domain.com/scheduled \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"task": "reset-accounts", "force": true}'

# 测试初始化账号任务
curl -X POST https://your-domain.com/scheduled \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"task": "init-accounts", "force": true}'
```

### 3. 直接测试 Cron 任务
```bash
# 直接测试重置账号 Cron
curl -X POST https://your-domain.com/newaccount/reset-accounts-cron \
  -H "Authorization: Bearer YOUR_TOKEN"

# 直接测试初始化账号 Cron
curl -X POST https://your-domain.com/newaccount/init-accounts-cron \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 监控和日志

### 1. Cloudflare Workers 日志
- 在 Cloudflare Dashboard 中查看 Workers 日志
- 所有 Cron 任务都会输出详细的执行日志

### 2. 关键监控指标
- 任务执行时间
- 处理的账号数量
- 外部 API 响应状态
- 错误率和失败原因

## 故障排除

### 1. Cron 任务未执行
- 检查 Cloudflare Workers 的调度配置
- 验证 `wrangler.jsonc` 中的 cron 表达式
- 查看 Workers 日志中的错误信息

### 2. 外部 API 调用失败
- 验证 API 端点的可用性
- 检查网络连接和防火墙设置
- 确认 API 认证和权限

### 3. 数据库查询问题
- 验证数据库连接
- 检查表结构和字段类型
- 确认查询条件的正确性

## 注意事项

1. **时区设置**: 所有时间都使用 Asia/Shanghai 时区
2. **API 端点**: 部署前必须更新为实际的 API 地址
3. **错误处理**: 任务失败不会影响其他调度任务
4. **数据一致性**: 确保数据库状态字段的准确性
5. **性能考虑**: 每次最多处理10个账号，避免长时间运行

## 成功验证

✅ 所有测试通过 (7/7)
✅ Cron 任务正确路由
✅ 数据库查询正常
✅ 错误处理完善
✅ 日志记录详细

项目已准备好部署到生产环境！
